2025-06-11 14:09:47.318 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 8961 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:09:47.319 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:09:47.320 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:09:48.018 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:09:48.019 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:09:48.020 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:09:48.020 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:09:48.020 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:09:48.020 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:09:48.618 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnwnP3A0dt8sul-FV4CkgAAAAA"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnwnP3A0dt8sul-FV4CkgAAAAA"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:09:48.624 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:09:48.624 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:09:48.624 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:09:48.624 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:09:48.800 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"O4AF8WEB100%7EaEnwnP3J0I_8Pyd-BQm8YQAAAAg"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"O4AF8WEB100%7EaEnwnP3J0I_8Pyd-BQm8YQAAAAg"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:09:48.801 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:09:48.807 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:09:48.807 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:09:48.807 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:09:48.807 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:09:48.948 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"M4k-rWEB100%7EaEnwnP370Db84xP-7LP1pAAAABI"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"M4k-rWEB100%7EaEnwnP370Db84xP-7LP1pAAAABI"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:09:48.948 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:09:49.342 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.366 seconds (process running for 2.748)
2025-06-11 14:09:49.344 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:09:49.344 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:09:49.344 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:09:49.344 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:09:49.344 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:09:49.345 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:09:49.345 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:09:49.345 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:09:49.345 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:09:49.345 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:09:49.347 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:09:49.347 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:12:49.152 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:12:49.153 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:12:49.153 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:12:59.840 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 11015 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:12:59.842 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:12:59.842 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:13:00.497 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:13:00.498 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:13:00.500 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:13:00.500 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:13:00.500 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:13:00.500 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:13:00.960 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnxXP3A0dt8sul-FV4CmAAAAAA"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnxXP3A0dt8sul-FV4CmAAAAAA"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:13:00.965 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:13:00.966 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:13:00.966 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:13:00.966 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:13:01.135 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fhdzsWEB100%7EaEnxXP3M0XB81yE-kj9mLQAAAAE"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fhdzsWEB100%7EaEnxXP3M0XB81yE-kj9mLQAAAAE"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:13:01.135 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:13:01.137 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:13:01.137 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:13:01.137 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:13:01.137 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:13:01.319 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"c4k-rWEB100%7EaEnxXP370Db84xP-7LP1qgAAABI"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"c4k-rWEB100%7EaEnxXP370Db84xP-7LP1qgAAABI"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:13:01.319 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:13:01.745 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.248 seconds (process running for 2.644)
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:13:01.747 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:13:01.747 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:13:01.747 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:13:01.748 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:13:01.749 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:13:01.750 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:21:57.257 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:21:57.257 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:21:57.257 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:23:27.229 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 14774 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:23:27.230 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:23:27.231 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:23:27.976 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:23:27.978 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:23:27.979 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:23:27.979 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:23:27.979 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:23:27.979 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:23:28.719 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnz0P3A0dt8sul-FV4CqQAAAAA"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EVhLpWEB100%7EaEnz0P3A0dt8sul-FV4CqQAAAAA"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:23:28.724 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:23:28.725 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:23:28.725 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:23:28.725 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:23:28.919 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"Bn3GHWEB100%7EaEnz0P3c0z88puO-6gwzYQAAAAI"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"Bn3GHWEB100%7EaEnz0P3c0z88puO-6gwzYQAAAAI"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:23:28.920 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:23:28.922 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:23:28.922 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:23:28.922 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:23:28.922 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:23:29.073 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WyE-HWEB100%7EaEnz0P3Z0LM8ijF-ZIdlPwAAABQ"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WyE-HWEB100%7EaEnz0P3Z0LM8ijF-ZIdlPwAAABQ"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:23:29.074 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:23:29.515 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.658 seconds (process running for 3.013)
2025-06-11 14:23:29.517 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:23:29.517 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:23:29.518 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:23:29.518 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:23:29.518 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:23:29.518 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:23:29.518 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:23:29.518 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:23:29.518 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:23:29.518 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:23:29.521 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:23:29.521 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:24:21.120 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:24:21.120 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:24:21.120 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:26:07.894 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 16828 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:26:07.895 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:26:07.896 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:26:08.600 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:26:08.601 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:26:08.602 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:26:08.602 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:26:08.602 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:26:08.602 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:26:09.133 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fPySAWEB100%7EaEn0cP3K0Ab8ySZ-0S1pdQAAAA0"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fPySAWEB100%7EaEn0cP3K0Ab8ySZ-0S1pdQAAAA0"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:26:09.139 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:26:09.140 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:26:09.140 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:26:09.140 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:26:09.481 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"UyYERWEB100%7EaEn0cP390N_8qjS-DTcXygAAAAU"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"UyYERWEB100%7EaEn0cP390N_8qjS-DTcXygAAAAU"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:26:09.481 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:26:09.483 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:26:09.483 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:26:09.483 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:26:09.483 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:26:09.617 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WyE-HWEB100%7EaEn0cP3Z0LM8ijF-ZIdlRQAAABQ"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WyE-HWEB100%7EaEn0cP3Z0LM8ijF-ZIdlRQAAABQ"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:26:09.618 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:26:10.008 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.453 seconds (process running for 2.806)
2025-06-11 14:26:10.009 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:26:10.009 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:26:10.009 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:26:10.010 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:26:10.010 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:26:10.010 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:26:10.010 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:26:10.010 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:26:10.010 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:26:10.010 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:26:10.012 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:26:10.012 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:30:26.162 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:30:26.162 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:30:26.162 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:30:40.436 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 18834 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:30:40.438 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:30:40.438 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:30:41.090 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:30:41.091 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:30:41.093 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:30:41.093 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:30:41.093 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:30:41.093 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:30:42.050 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"bQeHJWEB100%7EaEn1gP3M0T38UUH-KK5FfQAAAAQ"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"bQeHJWEB100%7EaEn1gP3M0T38UUH-KK5FfQAAAAQ"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:30:42.055 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:30:42.055 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:30:42.055 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:30:42.055 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:30:42.241 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"nIz9yWEB100%7EaEn1gP3C0cD82a4-D0dgvwAAAAk"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"nIz9yWEB100%7EaEn1gP3C0cD82a4-D0dgvwAAAAk"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:30:42.241 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:30:42.243 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:30:42.244 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:30:42.244 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:30:42.244 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:30:42.376 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"h6kSnWEB100%7EaEn1gP3A0xz8Ng_-wYdcbgAAABE"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"h6kSnWEB100%7EaEn1gP3A0xz8Ng_-wYdcbgAAABE"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:30:42.377 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:30:42.852 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.761 seconds (process running for 3.199)
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:30:42.854 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:30:42.855 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:30:42.855 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:30:42.855 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:30:42.856 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:30:42.856 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:53:31.127 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:53:31.127 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:53:31.127 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:54:36.920 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 24918 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:54:36.921 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:54:36.922 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:54:37.652 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:54:37.653 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:54:37.655 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:54:37.655 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:54:37.655 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:54:37.655 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:54:38.385 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"q8aB4WEB100%7EaEn7HP3j0xq8pSK-b07xbwAAAAI"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"q8aB4WEB100%7EaEn7HP3j0xq8pSK-b07xbwAAAAI"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:54:38.391 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:54:38.391 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:54:38.391 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:54:38.391 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:54:38.544 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"gIx1IWEB100%7EaEn7HP3_0WM8wDD-BtDR5QAAAAw"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"gIx1IWEB100%7EaEn7HP3_0WM8wDD-BtDR5QAAAAw"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:54:38.544 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:54:38.546 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:54:38.546 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:54:38.546 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:54:38.546 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:54:38.834 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"myE-HWEB100%7EaEn7HP3Z0LM8ijF-ZIdllQAAABQ"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"myE-HWEB100%7EaEn7HP3Z0LM8ijF-ZIdllQAAABQ"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:54:38.834 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:54:39.363 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.781 seconds (process running for 3.104)
2025-06-11 14:54:39.364 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:54:39.365 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:54:39.365 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:54:39.365 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:54:39.365 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:54:39.367 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:54:39.367 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 14:57:45.984 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 14:57:45.986 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 14:57:45.986 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 14:57:51.170 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 26530 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 14:57:51.171 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 14:57:51.172 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 14:57:51.900 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 14:57:51.901 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 14:57:51.903 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:57:51.903 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:57:51.903 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:57:51.903 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:57:52.689 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"OyDJtWEB100%7EaEn74P3o0Wt8GvN-VKCfFQAAAAA"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"OyDJtWEB100%7EaEn74P3o0Wt8GvN-VKCfFQAAAAA"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:57:52.694 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:57:52.694 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:57:52.694 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:57:52.694 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:57:52.833 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EyYERWEB100%7EaEn74P390N_8qjS-DTcYGQAAAAU"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"EyYERWEB100%7EaEn74P390N_8qjS-DTcYGQAAAAU"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:57:52.834 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 14:57:52.836 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 14:57:52.836 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 14:57:52.836 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 14:57:52.836 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 14:57:53.016 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"AIx1IWEB100%7EaEn74P3_0WM8wDD-BtDR8gAAAAw"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"AIx1IWEB100%7EaEn74P3_0WM8wDD-BtDR8gAAAAw"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 14:57:53.017 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 14:57:53.439 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.614 seconds (process running for 2.967)
2025-06-11 14:57:53.441 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 14:57:53.441 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 14:57:53.441 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 14:57:53.441 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 14:57:53.441 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 14:57:53.442 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 14:57:53.442 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 14:57:53.442 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 14:57:53.442 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 14:57:53.442 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 14:57:53.445 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 14:57:53.445 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 15:03:12.925 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 15:03:12.927 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 15:03:12.927 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
2025-06-11 15:11:15.564 [main] INFO  o.s.a.m.s.s.McpServerApplication - Starting McpServerApplication v0.1.0 using Java 21.0.6 with PID 28651 (/Volumes/Case_Sensitive/projects/mcp.server.query/target/mcp-query-stdio-server-0.1.0.jar started by jameswang in /Volumes/Case_Sensitive/projects/ia-ds-bulk)
2025-06-11 15:11:15.564 [main] DEBUG o.s.a.m.s.s.McpServerApplication - Running with Spring Boot v3.3.6, Spring v6.1.15
2025-06-11 15:11:15.565 [main] INFO  o.s.a.m.s.s.McpServerApplication - The following 1 profile is active: "stdio"
2025-06-11 15:11:16.240 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - AuthService constructor called with properties: null
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Properties is null: true
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_CLIENT_ID: null
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - System properties - OAUTH2_USERNAME: null
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_CLIENT_ID: null
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Environment variables - OAUTH2_USERNAME: null
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using system properties: clientId=, username=
2025-06-11 15:11:16.241 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Final configuration: clientId=, username=, baseUrl=https://partner.intacct.com/ia3/api/v1-beta2
2025-06-11 15:11:16.243 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 15:11:16.243 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 15:11:16.243 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 15:11:16.243 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 15:11:16.944 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"LQeHJWEB100%7EaEn_BP3M0T38UUH-KK5F2QAAAAQ"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"LQeHJWEB100%7EaEn_BP3M0T38UUH-KK5F2QAAAAQ"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.AuthService.init(AuthService.java:202)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMethod.invoke(InitDestroyAnnotationBeanPostProcessor.java:457)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor$LifecycleMetadata.invokeInitMethods(InitDestroyAnnotationBeanPostProcessor.java:401)
	at org.springframework.beans.factory.annotation.InitDestroyAnnotationBeanPostProcessor.postProcessBeforeInitialization(InitDestroyAnnotationBeanPostProcessor.java:219)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.applyBeanPostProcessorsBeforeInitialization(AbstractAutowireCapableBeanFactory.java:422)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.initializeBean(AbstractAutowireCapableBeanFactory.java:1798)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:600)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 15:11:16.949 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 15:11:16.949 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 15:11:16.950 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 15:11:16.950 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 15:11:17.091 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fC_5KWEB100%7EaEn_BP3G0O98PhY-tt2oZAAAAAY"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"fC_5KWEB100%7EaEn_BP3G0O98PhY-tt2oZAAAAAY"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.ModelService.<init>(ModelService.java:38)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 15:11:17.092 [main] ERROR o.s.a.mcp.sample.server.ModelService - Failed to obtain access token during initialization. ModelService may not function correctly.
2025-06-11 15:11:17.095 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Cached token is null or expired. Fetching new access token...
2025-06-11 15:11:17.095 [main] INFO  o.s.ai.mcp.sample.server.AuthService - Calling Intacct token endpoint...
2025-06-11 15:11:17.095 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using token endpoint: https://partner.intacct.com/ia3/api/v1-beta2/oauth2/token
2025-06-11 15:11:17.095 [main] DEBUG o.s.ai.mcp.sample.server.AuthService - Using client ID: 
2025-06-11 15:11:17.361 [main] ERROR o.s.ai.mcp.sample.server.AuthService - Error fetching new access token: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WCIaGWEB100%7EaEn_BP3e0rN81Po-yKvFFwAAAAs"}}"
org.springframework.web.client.HttpClientErrorException$BadRequest: 400 Bad Request: "{"error":{"code":"invalidRequest","message":"The Client ID, Client Secret, and\/or 3rd Party Application are incorrect","errorId":"REST-1216","additionalInfo":{"messageId":"IA.THE_CLIENT_ID_CLIENT_SECRET_AND_OR_3RD_PARTY_APPLICATION_ARE_INCORRECT","placeholders":{},"propertySet":{}},"supportId":"WCIaGWEB100%7EaEn_BP3e0rN81Po-yKvFFwAAAAs"}}"
	at org.springframework.web.client.HttpClientErrorException.create(HttpClientErrorException.java:103)
	at org.springframework.web.client.StatusHandler.lambda$defaultHandler$3(StatusHandler.java:86)
	at org.springframework.web.client.StatusHandler.handle(StatusHandler.java:146)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.applyStatusHandlers(DefaultRestClient.java:711)
	at org.springframework.web.client.DefaultRestClient.readWithMessageConverters(DefaultRestClient.java:200)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.readBody(DefaultRestClient.java:698)
	at org.springframework.web.client.DefaultRestClient$DefaultResponseSpec.body(DefaultRestClient.java:644)
	at org.springframework.ai.mcp.sample.server.AuthService.fetchNewAccessToken(AuthService.java:165)
	at org.springframework.ai.mcp.sample.server.AuthService.getAccessToken(AuthService.java:123)
	at org.springframework.ai.mcp.sample.server.QueryService.<init>(QueryService.java:40)
	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:502)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:486)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:208)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:117)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:315)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:306)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1375)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1212)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:975)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:971)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:625)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:754)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:456)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:335)
	at org.springframework.ai.mcp.sample.server.McpServerApplication.main(McpServerApplication.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:102)
	at org.springframework.boot.loader.launch.Launcher.launch(Launcher.java:64)
	at org.springframework.boot.loader.launch.JarLauncher.main(JarLauncher.java:40)
2025-06-11 15:11:17.361 [main] ERROR o.s.a.mcp.sample.server.QueryService - Failed to obtain access token during initialization. QueryService may not function correctly.
2025-06-11 15:11:17.853 [main] INFO  o.s.a.m.s.s.McpServerApplication - Started McpServerApplication in 2.666 seconds (process running for 3.038)
2025-06-11 15:11:17.855 [main] INFO  o.s.a.m.s.s.McpServerApplication - MCP Server 'query-server' v0.1.0 is ready
2025-06-11 15:11:17.855 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active profiles: stdio
2025-06-11 15:11:17.855 [main] INFO  o.s.a.m.s.s.McpServerApplication - Active transports: []
2025-06-11 15:11:17.856 [main] INFO  o.s.a.m.s.s.McpServerApplication - Available tool providers: ModelService, QueryService
2025-06-11 15:11:17.856 [main] INFO  o.s.a.m.s.s.McpServerApplication - Server capabilities: resourceChangeNotification=true, toolChangeNotification=true, promptChangeNotification=true
2025-06-11 15:11:17.856 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing MCP transports...
2025-06-11 15:11:17.856 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initializing STDIO transport...
2025-06-11 15:11:17.856 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Console logging disabled for STDIO compatibility
2025-06-11 15:11:17.856 [main] DEBUG o.s.a.m.s.s.t.TransportManager - Spring Boot banner disabled for STDIO compatibility
2025-06-11 15:11:17.856 [main] DEBUG o.s.a.m.s.s.security.SecurityContext - Setting authentication context: AuthenticationContext{transportMode=STDIO, sessionId='null', authenticated=false, expired=false}
2025-06-11 15:11:17.858 [main] INFO  o.s.a.m.s.s.t.TransportManager - STDIO transport initialized successfully
2025-06-11 15:11:17.858 [main] INFO  o.s.a.m.s.s.t.TransportManager - Initialized transports: [STDIO]
2025-06-11 15:13:00.656 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Shutting down transport manager...
2025-06-11 15:13:00.659 [SpringApplicationShutdownHook] DEBUG o.s.a.m.s.s.security.SecurityContext - Clearing authentication context
2025-06-11 15:13:00.660 [SpringApplicationShutdownHook] INFO  o.s.a.m.s.s.t.TransportManager - Transport manager shutdown complete
