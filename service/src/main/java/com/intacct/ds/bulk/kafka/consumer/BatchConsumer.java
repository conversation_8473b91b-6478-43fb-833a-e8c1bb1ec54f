package com.intacct.ds.bulk.kafka.consumer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.intacct.config.AppConfiguration;
import com.intacct.config.SecretsConfig;
import com.intacct.config.data.Pod;
import com.intacct.core.Context;
import com.intacct.core.ContextData;
import com.intacct.core.SpringContext;
import com.intacct.core.api.accesscontrol.OAuth2AccessTokenResponse;
import com.intacct.core.api.client.DSCacheAPIOwners;
import com.intacct.core.api.client.IARemoteException;
import com.intacct.core.api.framework.ExtraParameters;
import com.intacct.core.api.framework.RequestPath;
import com.intacct.core.api.utils.APIUtil;
import com.intacct.core.errorhandling.IAException;
import com.intacct.core.errorhandling.IAExceptionHandler;
import com.intacct.core.mongo.DSCoreMongoException;
import com.intacct.core.observation.ObservationUtility;
import com.intacct.ds.bulk.client.BatchFeignClient;
import com.intacct.ds.bulk.client.TokenFeignClient;
import com.intacct.ds.bulk.errorhandling.BulkException;
import com.intacct.ds.bulk.kafka.producer.BatchProducer;
import com.intacct.ds.bulk.model.*;
import com.intacct.ds.bulk.mongo.FileDAO;
import com.intacct.ds.bulk.mongo.JobDAO;
import com.intacct.ds.bulk.mongo.ObjectDAO;
import com.intacct.utils.Utils;
import feign.Request;
import feign.RetryableException;
import io.micrometer.observation.annotation.Observed;
import io.micrometer.tracing.Tracer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.core.query.Collation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.*;
import org.springframework.kafka.annotation.DltHandler;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.kafka.support.KafkaMessageHeaderAccessor;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.UndeclaredThrowableException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.*;

import static com.intacct.core.api.external.Utility.sendCallbackMessage;
import static com.intacct.core.api.framework.RequestPath.PATH_ELEMENT_DOMAIN;
import static com.intacct.core.api.framework.RequestPath.PATH_ELEMENT_LEAF_OBJECT;
import static com.intacct.core.api.registry.APIVersionRegistry.DEFAULT_OWNER_DOMAIN;
import static com.intacct.utils.Constants.EMPTY_PLACEHOLDERS;

@Service("batchConsumer")
@Slf4j
@RequiredArgsConstructor
public class BatchConsumer {

    public static final String OBJECTS = "/objects/";
    public static final String BATCH_INFO_STATUS    = "batchInfo.status";
    public static final String BATCH_INFO_JOB_ID    = "batchInfo.jobId";
    public static final String IDEMPOTENCY_KEY      = "Idempotency-Key";

    private static final long ONE_MINUTE_IN_SECONDS = 60L;

    @Value("${app.ds.bulk.batch-commit: false}")
    private boolean batchCommitFlag;

    @Value("${app.ds.bulk.job.expiration:2}")
    private float expirationDays;

    private final JobDAO jobDAO;
    private final Context context;
    private final ObjectMapper objectMapper;
    private final BatchProducer batchProducer;
    private final DSCacheAPIOwners dsCacheAPIOwners;
    private final SecretsConfig secretsConfig;
    private final TokenFeignClient tokenFeignClient;
    private final BatchFeignClient batchFeignClient;

    // Static instance of RetryableException
    public static final RetryableException RETRYABLE_EXCEPTION = new RetryableException(
            HttpStatus.CONFLICT.value(),
            "Conflict response from RESTApi client.",
            Request.HttpMethod.GET,
            null,
            0L,
            Request.create(Request.HttpMethod.GET, "", new HashMap<>(), Request.Body.empty(), null)
    );

    @Observed(name = "kafka.batch.consume")
    @KafkaListener(topics = "${app.ds.bulk.batch.topic}", groupId = "${app.ds.bulk.batch.groupid}", containerFactory = "batchListenerContainerFactory")
    public void consume(BatchInfo batchInfo, KafkaMessageHeaderAccessor headerAccessor) throws DSCoreMongoException, IOException, IARemoteException {

        log.info("Received batch info: {}", Utils.toJSON(batchInfo));
        log.info("Delivery Attempt: {}", headerAccessor.getNonBlockingRetryDeliveryAttempt());

        ContextData requestContext = batchInfo.getContextDataHelper().getContextData();
        log.info("Context: {}", Utils.toJSON(context.getData()));
        log.info("Request Context data: {}", Utils.toJSON(requestContext));
        context.setData(requestContext);
        log.info("Updated Context: {}", Utils.toJSON(context.getData()));

        try {
            ObjectDAO<Batch> batchDAO = new ObjectDAO<>(context, Batch.class);
            Batch batch = getBatch(batchInfo.getId(), batchDAO);
            // batch could not be found in mongo
            if (batch == null) {
                // consume the message and return if batch is not found in mongoDB
                logNotFoundError("Batch", batchInfo.getId());
                return;
            }

            // mark status as in progress
            batchInfo.setStatus(BatchStatus.PROCESSING);
            batch.setBatchInfo(batchInfo);
            String idempotencyKey = batch.getIdempotencyKey();
            if (idempotencyKey == null || idempotencyKey.isEmpty()) {
                batch.setIdempotencyKey(batchInfo.getId());
            }
            batchDAO.updateObject(batch);

            // execute batch
            String response = executeBatch(batch, headerAccessor);

            // update batch status to completed
            batchInfo.setStatus(BatchStatus.COMPLETED);
            batch.getBatchPayload().setResponse(response);
            batch.setCompletedDateTime(new Date());
            batch.setBatchInfo(batchInfo);
            batchDAO.setTTL(expirationDays);
            batchDAO.updateObject(batch);
            log.info("Batch processing successful: {}", batchInfo.getId());

            // update job with batch completion
            updateJobStatus(batchInfo, batchDAO);

        } catch (Exception e) {
            log.error("Error processing batch: {}", e.getMessage());
            throw e;    // retryable exception will be requeued
        }
    }

    private void updateJobStatus(BatchInfo batchInfo, ObjectDAO<Batch> batchDAO) throws DSCoreMongoException {
        ObjectDAO<Job> jobDAO = new ObjectDAO<>(context, Job.class);
        MongoOperations mongoTemplate = jobDAO.getMongoTemplateInTenantContext();

        // Use findAndModify to atomically update and return the modified document in a single operation
        Query query = new Query(Criteria.where("id").is(batchInfo.getJobId()));
        Update update = new Update().addToSet("completedBatches", batchInfo.getId());

        // FindAndModify returns the updated document and performs the update atomically
        Job job = mongoTemplate.findAndModify(
                query,
                update,
                FindAndModifyOptions.options().returnNew(true),
                Job.class
        );

        if (job != null) {
            log.info("Job {} updated: completedBatchCount={}", job.getId(), job.getCompletedBatchCount());

            // queue next batch
            queueNextBatch(job, batchDAO);

            // if all batches are processed, then publish results
            publishResults(job, jobDAO, batchDAO);

        } else {
            logNotFoundError("Job", batchInfo.getJobId());
            // consume the message and return if job not found in mongoDB
        }
    }

    private void queueNextBatch(Job job, ObjectDAO<Batch> batchDAO) {
        log.info("Entering queueNextBatch method =======================================>");
        log.info("Queueing next batch for job: {}", job.getId());
        // check if there are more batches to be queued
        if (InternalJobStatus.PROCESSING == job.getStatus()) {
            try {
                // Define collation for case-insensitive comparison
                log.info("Starting to query for next batch, Job ID: {}", job.getId());
                Collation collation = Collation.of("en").strength(Collation.ComparisonLevel.secondary());
                Query query = new Query(new Criteria().andOperator(
                        Criteria.where(BATCH_INFO_STATUS).is(BatchStatus.CREATED),
                        Criteria.where(BATCH_INFO_JOB_ID).is(job.getId())
                )).collation(collation);
                Update update = new Update().set(BATCH_INFO_STATUS, BatchStatus.QUEUED);
                MongoOperations mongoTemplate = batchDAO.getMongoTemplateInTenantContext();
                Batch nextBatch = mongoTemplate.update(Batch.class)
                        .matching(query)
                        .apply(update)
                        .withOptions(FindAndModifyOptions.options().returnNew(true))
                        .findAndModifyValue();

                if (nextBatch != null) {
                    log.info("Next batch found: {}", nextBatch.getId());
                    batchProducer.send(nextBatch.getBatchInfo());
                    log.info("Next batch queued for job: {}", job.getId());
                } else {
                    log.info("No next batch found, job complete: {}", job.getId());
                }

            } catch (DSCoreMongoException e) {
                log.error("Error updating next batch for job in mongo: {}", e.getMessage());
            }
        }
        log.info("Exiting queueNextBatch method <========================================");
    }

    private void publishResults(Job job, ObjectDAO<Job> jobDAO, ObjectDAO<Batch> batchDAO) {
        log.info("Entering publishResults method =======================================>");
        log.info("Publishing results for job: {}, batch count: {}", job.getId(), job.getBatchCount());

        //get batch response and write to file
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {

            Batch nextBatch = getUncompletedBatch(job, batchDAO);
            if (nextBatch != null) {
                log.info("Uncompleted batch found: {}", nextBatch.getId());
                return;
            }
            log.info("All batches completed for job: {}", job.getId());

            //set job status to post-processing while creating response file
            job.setStatus(InternalJobStatus.MERGE_BATCH_RESPONSE);
            jobDAO.updateObject(job);

            bos.write('[');
            int count = 0;
            for (String batchId : job.getBatchIdList()) {
                log.info("Processing batch response, Batch ID: {}", batchId);
                // get batch payload from mongo
                Batch batch = batchDAO.getObject(batchId);
                if (batch == null) {
                    logNotFoundError("Batch", batchId);
                    // data corrupted, please skip this batch
                    continue;
                }

                String response = batch.getBatchPayload().getResponse();
                if (response == null) {
                    log.error("Batch response not found in mongo: {}", batchId);
                    // data corrupted, please skip this batch
                    continue;
                }
                if (count > 0) {
                    // add a separator
                    bos.write(',');
                }
                // trim the array brackets from response if present
                if (response.startsWith("[") && response.endsWith("]")) {
                    response = response.substring(1, response.length() - 1);
                }

                //write response to file
                bos.write(response.getBytes());
                count++;
            }
            bos.write(']');
            // upload file to mongo GFS
            String fileName = job.getId() + "_response.json";
            // TODO: the file size could be large, need to stream the content
            InputStream fileContentStream = new ByteArrayInputStream(bos.toByteArray());
            FileDAO fileDAO = new FileDAO(context);
            ObjectId objectId = fileDAO.storeFile(fileContentStream, fileName, "json");
            log.info("Response file stored in mongo: {}", objectId.toString());

            // update TTL date for response file
            fileDAO.setTTLDate(objectId, expirationDays);

            // update TTL date for request file
            fileDAO.setTTLDate(new ObjectId(job.getRequestFileId()), expirationDays);

            // update job status
            job.setCompletedDateTime(new Date());
            jobDAO.setTTL(expirationDays);
            job.setStatus(InternalJobStatus.COMPLETED);
            job.setResponseFileId(objectId.toString());
            jobDAO.updateObject(job);

            String callbackUrl = job.getCallbackUrl();
            if (callbackUrl != null && !callbackUrl.isEmpty()) {
                StatusResponseDTO payload = new StatusResponseDTO(job.getId(), job.getStatus().mapToJobStatus().getStatus(), 100);
                HttpStatusCode statusCode = sendCallbackMessage(payload, job.getCallbackUrl(), new ExtraParameters(), context);
                if (statusCode != HttpStatus.OK) {
                    log.error("Error sending callback message to client: {}", statusCode);
                } else {
                    log.info("Callback message sent successfully for job: {}", job.getId());
                }
            } else {
                log.debug("No callback URL found for job: {}", job.getId());
            }

        } catch (Throwable e) {
            log.error("Error publishing results for job: {}", job.getId(), e);
        } finally {
            log.info("Exiting publishResults method <========================================");
        }
    }

    private static Batch getUncompletedBatch(Job job, ObjectDAO<Batch> batchDAO) throws DSCoreMongoException {
        // check if all batches are completed
        Query query = new Query(new Criteria().andOperator(
                Criteria.where(BATCH_INFO_STATUS).ne(BatchStatus.COMPLETED),
                Criteria.where(BATCH_INFO_STATUS).ne(BatchStatus.FAILED),
                Criteria.where(BATCH_INFO_JOB_ID).is(job.getId())
        ));
        return batchDAO.queryObject(query);
    }

    private String executeBatch(Batch batch, KafkaMessageHeaderAccessor headerAccessor) throws IOException, IARemoteException {
        BatchInfo batchInfo = batch.getBatchInfo();
        String payload = batch.getBatchPayload().getRequest();
        log.info("Executing batch: {}, Operation: {}", batchInfo.getId(), batchInfo.getOperation());
        try {
            String jobId = batchInfo.getJobId();
            Job job = jobDAO.getObject(jobId);
            if (job == null) {
                logNotFoundError("Job", jobId);
                throw new BulkException("BULK-0006", Utils.toMap("JOB_ID", jobId));
            }

            String token = ensureValidToken(job.getToken(), batchInfo);
            // if token is diff from job.getToken(), update job token
            if (!token.equals(job.getToken())) {
                job.setToken(token);
                jobDAO.updateObject(job);
                log.info("Job token updated for Job ID: {}", jobId);
            }

            Map<String, String> headers = new HashMap<>();
            // set authorization header
            headers.put(HttpHeaders.AUTHORIZATION, token);

            // get timestamp and retry count for debug headers
            Long timestamp = headerAccessor.getTimestamp();
            int retryCount = headerAccessor.getNonBlockingRetryDeliveryAttempt();
            headers.put("X-DEBUG-TIMESTAMP", String.valueOf(timestamp));
            headers.put("X-DEBUG-RETRY-COUNT", String.valueOf(retryCount));

            String traceparent = (String) headerAccessor.getHeader("traceparent");
            log.info("traceparent {} set for Batch ID: {} ",  traceparent, batch.getId());
            // set traceparent header if not present, generate a new one
            if (traceparent == null || traceparent.isEmpty()) {
                Tracer tracer = SpringContext.getBean(Tracer.class);
                traceparent = ObservationUtility.buildTraceParentHeader(tracer);
                log.info("Generate traceparent {} for Batch ID: {} ", traceparent, batch.getId());
            }
            headers.put("traceparent", traceparent);

            // all or none header, useful for avoiding duplicates in case of system failure
            headers.put("X-IA-Batch-Commit", String.valueOf(batchCommitFlag));

            String requestPath = batchInfo.getVersion() + OBJECTS + batchInfo.getObjectName();
            Map<String, String> pathElementMap = RequestPath.parsePath(requestPath);
            String domain = this.getDomain(requestPath, pathElementMap.get("domain"));
            String baseUrl = getBaseUrl(domain);

            ResponseEntity<String> response;
            switch(batchInfo.getOperation()) {
                case "create":
                    log.info("Executing create operation, Batch ID: {}", batchInfo.getId());
                    // set idempotency key header
                    log.info("Idempotency key {} set for Batch ID: {}", batch.getIdempotencyKey(), batchInfo.getId());
                    headers.put(IDEMPOTENCY_KEY, batch.getIdempotencyKey());
                    headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                    response = batchFeignClient.batchCreate(baseUrl, batchInfo.getVersion(), pathElementMap.get(PATH_ELEMENT_DOMAIN), pathElementMap.get(PATH_ELEMENT_LEAF_OBJECT), headers, payload);
                    break;
                case "update":
                    log.info("Executing update operation, Batch ID: {}", batchInfo.getId());
                    // set idempotency key header
                    log.info("Idempotency key {} set for Batch ID: {}", batch.getIdempotencyKey(), batchInfo.getId());
                    headers.put(IDEMPOTENCY_KEY, batch.getIdempotencyKey());
                    headers.put(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE);
                    response = batchFeignClient.batchUpdate(baseUrl, batchInfo.getVersion(), pathElementMap.get(PATH_ELEMENT_DOMAIN), pathElementMap.get(PATH_ELEMENT_LEAF_OBJECT), headers, payload);
                    break;
                case "delete":
                    log.info("Executing delete operation, Batch ID: {}", batchInfo.getId());
                    String keys = convertToBulkDeletePayload(payload);
                    Map<String, Object> queryParam = new HashMap<>();
                    queryParam.put("keys", keys);
                    response = batchFeignClient.batchDelete(baseUrl, batchInfo.getVersion(), pathElementMap.get(PATH_ELEMENT_DOMAIN), pathElementMap.get(PATH_ELEMENT_LEAF_OBJECT), queryParam, headers);
                    if (response.getStatusCode() == HttpStatus.NO_CONTENT) {
                        response = createBulkDeleteResponse(batchInfo);
                    }
                    break;
                default:
                    log.error("Invalid operation: {}, Batch ID: {}", batchInfo.getOperation(), batchInfo.getId());
                    return ResponseEntity.status(500).body("Invalid operation").toString();
            }
            log.debug("Response received for Batch ID: {}, Status: {}", batchInfo.getId(), response.getStatusCode());
            String responseBody = response.getBody();
            if (responseBody == null || responseBody.isEmpty()) {
                log.error("Empty response for Batch ID: {}", batchInfo.getId());
                return ResponseEntity.status(500).body("Empty response").toString();
            }
            log.debug("Response body for Batch ID: {} ------{}", batchInfo.getId(), responseBody);
            JsonNode responseNode = objectMapper.readTree(responseBody);
            Optional<JsonNode> resultNode = Optional.ofNullable(responseNode.get("ia::result"));
            if (resultNode.isPresent()) {
                log.info("Results for Batch ID: {} ------{}", batchInfo.getId(), resultNode.get().toString());
                return resultNode.get().toString();
            } else {
                log.error("Error processing response json for Batch ID: {}", batchInfo.getId());
                return ResponseEntity.status(500).body("Error processing response json").toString();
            }
        } catch (JsonProcessingException e) {
            log.error("JsonProcessingException error processing response json for Batch ID: {}", batchInfo.getId(), e);
            return ResponseEntity.status(500).body(e.getMessage()).toString(); // accept exception as response
        } catch (RetryableException e) {
            log.error("RetryableException error posting to BatchFeignClient. Batch ID: {}", batchInfo.getId(), e);
            throw e;    // requeue the message for retryable exception
        } catch (UndeclaredThrowableException e) {
            log.error("UndeclaredThrowableException error posting to BatchFeignClient. Batch ID: {}", batchInfo.getId(), e);
            if (e.getCause() instanceof IARemoteException iaRemoteException) {
                HttpStatus httpStatus = iaRemoteException.getStatusLookup() != null
                        ? iaRemoteException.getStatusLookup().getStatusCode()
                        : HttpStatus.INTERNAL_SERVER_ERROR;
                if (HttpStatus.CONFLICT == httpStatus) {
                    throw RETRYABLE_EXCEPTION;    // requeue the message for retryable exception
                }
                log.info("HTTP Status: {}", httpStatus);
                if (httpStatus.is5xxServerError()) {
                    throw e;    // requeue the message for 5xx errors
                }
            }
            return handleErrorResponse(e, batchInfo);   // non-retryable exception will be handled internally
        } catch (IOException e) {
            log.error("IOException error processing response for Batch ID: {}. Error cause: {}", batchInfo.getId(), e.getMessage(), e);
            throw e;    // requeue the message for IO exception
        } catch (IARemoteException e) {     // returned by the ErrorResponseDecoder of BatchFeignClient
            log.error("IARemoteException error posting to BatchFeignClient. Batch ID: {}", batchInfo.getId(), e);
            log.error("IARemoteException error message: {}", e.getMessage());
            if (null != e.getCause()) {
                log.error("Error cause: {}", e.getCause().getMessage(), e.getCause());
            }
            if (e.getHttpStatus().is5xxServerError()) {
                throw e;    // requeue the message for 5xx errors
            }
            return handleErrorResponse(e, batchInfo);   // non-retryable exception will be handled internally
        } catch (Exception e) {
            log.error("Exception error when posting to BatchFeignClient. Batch ID: {}", batchInfo.getId(), e);
            if (null != e.getCause()) {
                log.error("Error cause: {}", e.getCause().getMessage(), e.getCause());
            }
            return handleErrorResponse(e, batchInfo);   // non-retryable exception will be handled internally
        } catch (Throwable throwable) {
            log.error("Throwable error when posting to BatchFeignClient. Batch ID: {}", batchInfo.getId(), throwable);
            if (null != throwable.getCause()) {
                log.error("Throwable cause: {}", throwable.getCause().getMessage(), throwable.getCause());
            }
            return handleErrorResponse(throwable, batchInfo);   // non-retryable exception will be handled internally
        }
    }

    private String ensureValidToken(String token, BatchInfo batchInfo) throws JsonProcessingException, BulkException {
        if (isTokenExpired(token)) {
            log.info("Token expired, refreshing token...");
            return refreshToken(token, batchInfo);
        }
        log.info("Token is valid for Batch ID: {}", batchInfo.getId());
        return token;
    }

    private String refreshToken(String token, BatchInfo batchInfo) throws BulkException {
        try {
            JsonNode node = decodeJWT(token);
            assert node != null;
            String clientId = node.get("clientId").asText();
            String trustedContext = createTrustedContext(node);

            ResponseEntity<OAuth2AccessTokenResponse> response = tokenFeignClient.refreshToken(
                    batchInfo.getVersion(),
                    "refresh_token",
                    clientId,
                    secretsConfig.getTrustedServiceSecret(),
                    trustedContext
            );

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                String newToken = "Bearer " + response.getBody().getAccessToken();
                log.info("Token refreshed for Batch ID: {}", batchInfo.getId());
                return newToken;
            } else {
                log.error("Error refreshing token for Batch ID: {}", batchInfo.getId());
                throw new BulkException("BULK-0012", Utils.toMap("BATCH_ID", batchInfo.getId()));
            }
        } catch (Exception e) {
            log.error("Error refreshing token for Batch ID: {}", batchInfo.getId(), e);
            throw new BulkException("BULK-0012", Utils.toMap("BATCH_ID", batchInfo.getId()), e);
        }
    }

    private String createTrustedContext(JsonNode node) throws JsonProcessingException {
        // extract required fields from the JWT token
        String userId = node.path("userId").asText("");
        String cnyId = node.path("cnyId").asText("");
        String entityId = node.path("entityId").asText("");

        // construct the username
        String username = userId + "@" + cnyId + (entityId.isEmpty() ? "" : "|" + entityId);

        // create and populate the context map
        Map<String, String> contextMap = Map.of("username", username);

        // convert the context map to a JSON string
        String jsonContext = objectMapper.writeValueAsString(contextMap);

        // encode the JSON string to Base64
        return Base64.getEncoder().encodeToString(jsonContext.getBytes(StandardCharsets.UTF_8));
    }

    private JsonNode decodeJWT(String token) throws JsonProcessingException {
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            return null;
        }
        String payload = new String(Base64.getDecoder().decode(parts[1]));
        return objectMapper.readTree(payload);
    }

    private boolean isTokenExpired(String token) {
        try {
            String[] parts = token.split("\\.");
            if (parts.length != 3) {
                return true;
            }
            String payload = new String(Base64.getDecoder().decode(parts[1]));
            JsonNode node = objectMapper.readTree(payload);
            long exp = node.get("exp").asLong();
            log.info("Token expiry: {}", exp);
            log.info("Expiry + 1 minute: {}", exp + ONE_MINUTE_IN_SECONDS);
            log.info("Current time: {}", Instant.now().getEpochSecond());
            return Instant.now().getEpochSecond() >= exp + ONE_MINUTE_IN_SECONDS;
        } catch (Exception e) {
            log.error("Error parsing token: {}", e.getMessage());
            return true;
        }
    }

    private String convertToBulkDeletePayload(String payload) throws JsonProcessingException {
        JsonNode node = objectMapper.readTree(payload);
        StringBuilder keysBuilder = new StringBuilder();
        for (JsonNode child : node) {
            String key = child.get("key").asText();
            keysBuilder.append(key).append(",");
        }

        // Remove the last comma
        return keysBuilder.toString().replaceAll(",$", "");
    }

    private ResponseEntity<String> createBulkDeleteResponse(BatchInfo batchInfo) {
        // create a list of responses
        ArrayList<String> responseArray = new ArrayList<>();
        for (int i = 0; i < batchInfo.getBatchSize(); i++) {
            responseArray.add("{\"ia::status\":204}");
        }
        return ResponseEntity.ok("{\"ia::result\":" + responseArray + "}");
    }

    public ResponseEntity<String> handleRemoteExceptionResponse(Throwable e) {
        if (e instanceof UndeclaredThrowableException) {
            if (null != e.getCause()) {
                Throwable throwable = e.getCause();
                if (throwable instanceof IARemoteException iaRemoteException) {
                    if (null != iaRemoteException.getIaError()) {
                        // add null check for statusLookup
                        HttpStatus httpStatus = iaRemoteException.getStatusLookup() != null
                                ? iaRemoteException.getStatusLookup().getStatusCode()
                                : HttpStatus.INTERNAL_SERVER_ERROR;
                        iaRemoteException.getIaError().setStatusNumber(httpStatus.value());
                        iaRemoteException.getIaError().setStatusLookup(iaRemoteException.getStatusLookup());
                        iaRemoteException.getIaError().setReturningStatus(true);
                        return new IAExceptionHandler().handleAPIException(iaRemoteException, true, context);
                    }
                }
            }
        }
        return null;
    }

    public String handleErrorResponse(Throwable e, BatchInfo batchInfo) {
        ResponseEntity<String> response = handleRemoteExceptionResponse(e);
        if (response == null) {
            // create a response with internal error
            response = new IAExceptionHandler().handleAPIException(e, true, context);
        }
        String resultsString;
        try {
            ObjectNode responseNode = (ObjectNode) objectMapper.readTree(response.getBody());
            JsonNode results = responseNode.get("ia::result");
            log.info("Internal error results for Batch ID: {} ------{}", batchInfo.getId(), results.toString());
            resultsString = results.toString();
        } catch (JsonProcessingException ex) {
            log.error("Error creating internal error response json for Batch ID: {}", batchInfo.getId(), ex);
            resultsString = ResponseEntity.status(500).body(ex.getMessage()).toString(); // accept exception as response
        }
        // create a list of responses
        ArrayList<String> responseArray = new ArrayList<>();
        for (int i = 0; i < batchInfo.getBatchSize(); i++) {
            responseArray.add(resultsString);
        }
        return responseArray.toString();
    }

    @DltHandler
    public void handleDlt(@Nonnull ConsumerRecord<String, BatchInfo> event) {
        log.info("Entering handleDlt method =======================================>");
        BatchInfo batchInfo = event.value();
        log.info("Processing DLT event for batch: {}", batchInfo.getId());
        log.info("DLT event for batch: {}", batchInfo.toString());
        
        // log all error headers for debugging
        logErrorHeaders(event);

        // extract comprehensive error message from the headers
        String detailedError = extractErrorMessage(event);
        log.error("Detailed error message: {}", detailedError);

        // consume the message and fail the batch
        String errMsg = new String(event.headers().lastHeader(KafkaHeaders.EXCEPTION_MESSAGE).value());
        log.error("Error message from the DLT event: {}", errMsg);
        BulkException exception = new BulkException("BULK-0012", Utils.toMap("BATCH_ID", batchInfo.getId()));
        String message = handleErrorResponse(exception, batchInfo);
        log.info("Error message: {}", message);
        failBatch(batchInfo, message);
        log.info("Exiting handleDlt method <========================================");
    }

    private String extractErrorMessage(ConsumerRecord<String, BatchInfo> event) {
        StringBuilder errorBuilder = new StringBuilder();
        Headers headers = event.headers();
        Map<String, List<String>> errorHeaders = new HashMap<>();
        headers.forEach(header -> {
            String key = header.key();
            if (isErrorHeader(key)) {
                String value = new String(header.value(), StandardCharsets.UTF_8);
                errorHeaders.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
            }
        });

        // Process original exception message
        errorHeaders.getOrDefault(KafkaHeaders.EXCEPTION_MESSAGE, Collections.emptyList())
                .forEach(msg -> appendError(errorBuilder, "Exception", msg));

        // Process exception stacktrace
        errorHeaders.getOrDefault(KafkaHeaders.EXCEPTION_STACKTRACE, Collections.emptyList())
                .forEach(trace -> appendError(errorBuilder, "Stacktrace", trace));

        // Process retry attempt information
        String retryAttempt = extractRetryAttempt(headers);
        if (retryAttempt != null) {
            errorBuilder.append("Retry Attempt: ").append(retryAttempt).append("\n");
        }

        // If no error information found
        if (errorBuilder.length() == 0) {
            return "Unknown error occurred - no error details available in headers";
        }

        return errorBuilder.toString();
    }

    private void appendError(StringBuilder builder, String type, String message) {
        if (message != null && !message.trim().isEmpty()) {
            if (builder.length() > 0) {
                builder.append("\n");
            }
            builder.append(type).append(": ").append(message.trim());
        }
    }

    private String extractRetryAttempt(Headers headers) {
        Header retryHeader = headers.lastHeader("kafka_retry-topic-attempts");
        if (retryHeader != null) {
            return new String(retryHeader.value(), StandardCharsets.UTF_8);
        }
        return null;
    }

    private boolean isErrorHeader(String headerKey) {
        return headerKey != null && (
                headerKey.equals(KafkaHeaders.EXCEPTION_MESSAGE) ||
                        headerKey.equals(KafkaHeaders.EXCEPTION_STACKTRACE) ||
                        headerKey.equals(KafkaHeaders.EXCEPTION_CAUSE_FQCN) ||
                        headerKey.equals(KafkaHeaders.EXCEPTION_FQCN) ||
                        headerKey.contains("retry") ||
                        headerKey.contains("error")
        );
    }

    // helper method to format error messages for logging
    private void logErrorHeaders(ConsumerRecord<String, BatchInfo> event) {
        if (log.isDebugEnabled()) {
            log.debug("Error headers for batch {}:", event.value().getId());
            event.headers().forEach(header ->
                    log.debug("Header {} = {}",
                            header.key(),
                            new String(header.value(), StandardCharsets.UTF_8))
            );
        }
    }

    private void failBatch(BatchInfo batchInfo, String message) {
        log.info("Entering failBatch method =======================================>");
        if (batchInfo == null) {
            log.error("Batch info is null");
            return;
        }
        if (message == null) {
            log.error("Message is null");
            return;
        }
        log.error("Batch {} failed: {}", batchInfo.getId(), message);

        try {
            log.info("Starting to process failed batch, Batch ID: {}", batchInfo.getId());
            ContextData requestContext = batchInfo.getContextDataHelper().getContextData();
            context.setData(requestContext);
            ObjectDAO<Batch> batchDAO = new ObjectDAO<>(context, Batch.class);
            Batch batch = getBatch(batchInfo.getId(), batchDAO);
            // should not be null, as it is already reached DLT
            if (batch == null) {
                log.error("Failed batch not found in mongo: {}", batchInfo.getId());
                return;
            }
            BatchPayload payload = batch.getBatchPayload();
            payload.setResponse(message);
            batchInfo.setStatus(BatchStatus.FAILED);
            batch.setCompletedDateTime(new Date());
            batchDAO.setTTL(expirationDays);
            batch.setBatchInfo(batchInfo);
            batchDAO.updateObject(batch);
            log.info("Failed batch processed and updated in mongo: {}", batchInfo.getId());

            // update job with batch completion
            updateJobStatus(batchInfo, batchDAO);

        } catch (DSCoreMongoException e) {
            // for MongoDB operations
            log.error("Error updating failed batch in mongo: {}", batchInfo.getId(), e);
        } finally {
            log.info("Exiting failBatch method <========================================");
        }
    }

    private Batch getBatch(String batchId, ObjectDAO<Batch> batchDAO) throws DSCoreMongoException {
        log.info("Fetching batch from database, Batch ID: {}", batchId);
        try {
            Query query = new Query(Criteria.where("batchInfo.id").is(batchId));
            Batch batch = batchDAO.queryObject(query);
            if (batch != null) {
                log.info("Batch found: {}", batch.getId());
            } else {
                logNotFoundError("Batch", batchId);
            }
            return batch;

        } catch (DSCoreMongoException e) {
            log.error("Error getting batch {} from mongo: {}", batchId, e.getMessage());
            // should requeue to a delay topic
            throw e;
        }
    }

    private void logNotFoundError(String type, String id) {
        log.error("{} not found in mongo: {}", type, id);
    }

    private String getDomain(String requestPathPart, String domainElement) {
        String domain;
        if (domainElement != null && domainElement.equalsIgnoreCase("common")) {
            domain = SpringContext.getEnvPropConfig().getAppName();
        } else {
            domain = this.dsCacheAPIOwners.getRuntimeOwner(requestPathPart);
        }
        return domain;
    }

    private String getDSBaseUrl(String domain) throws Exception {
        AppConfiguration appConfig = context.getAppConfiguration();
        int currentPod = appConfig == null ? -1 : appConfig.getCurrentPOD();
        Pod pod = appConfig == null || appConfig.getPods() == null ? null : appConfig.getPods().get(currentPod);
        if (pod == null) {
            throw new IAException("DB-0006", EMPTY_PLACEHOLDERS, "", null);
        }
        return "https://" + pod.getApiDSEndpoint() + "/" + domain + "/ia/api/";
    }

    private String getBaseUrl(String domain) throws Exception {
        String baseUrl = domain.equals(DEFAULT_OWNER_DOMAIN)
                ? APIUtil.getPhpBaseUrl() + "/api/"
                : this.getDSBaseUrl(domain);
        log.debug("Retrieved base URL: {}", baseUrl);
        return baseUrl;
    }
}
